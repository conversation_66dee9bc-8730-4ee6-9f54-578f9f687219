import { GetToken } from "@clerk/types"

import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

export const fetchAllUsers = async () => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(`${API_BASE_URL}/users`, { headers })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching users", error)
    return []
  }
}

//fetch user with pagination
export const fetchUsersWithPagination = async (page: number, limit: number) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(
      `${API_BASE_URL}/users/pagination?page=${page}&limit=${limit}`,
      { headers },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching users with pagination", error)
    return []
  }
}

//fetch user by id
export const fetchUserById = async (id: string) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(`${API_BASE_URL}/users/${id}`, { headers })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching user by ID", error)
    return null
  }
}

//fetch user by clerk id
export const fetchUserByClerkId = async (clerkId: string) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(`${API_BASE_URL}/auth/clerk/${clerkId}`, {
      headers,
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching user by Clerk ID", error)
    return null
  }
}

// fetch user access to dashboard or not based on roles
export const fetchDashboardAccess = async (clerkId: string) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    if (!clerkId || clerkId === "") return null
    const res = await fetch(`${API_BASE_URL}/auth/dashboard/${clerkId}`, {
      headers,
      // next: { revalidate: 60 },
      cache: "no-store",
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching user dashboard access", error)
    return null
  }
}

//create user
export const createUser = async (user: any) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(`${API_BASE_URL}/users`, {
      method: "POST",
      headers,
      body: JSON.stringify(user),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating user", error)
    return null
  }
}

//update user
export const updateUser = async (user: any) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(`${API_BASE_URL}/users/${user.id}`, {
      method: "PUT",
      headers,
      body: JSON.stringify(user),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating user", error)
    return null
  }
}

//delete user
export const deleteUser = async (id: string) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(`${API_BASE_URL}/users/${id}`, {
      method: "DELETE",
      headers,
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting user", error)
    return null
  }
}

//fetch user by email
export const fetchUserByEmail = async (email: string) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
    "Content-Type": "application/json",
  }
  try {
    const res = await fetch(`${API_BASE_URL}/users?email=${email}`, {
      headers,
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching user by email", error)
    return null
  }
}
