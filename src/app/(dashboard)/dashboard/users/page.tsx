"use client"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Filter, Grid3X3, List, Plus, Search } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Suspense, useState } from "react"
import { toast } from "sonner"

import { fetchUsersWithPagination } from "@/actions/users"
import { UserCard } from "@/components/cards"
import { CustomBreadcrumbs } from "@/components/custom"
import { Container } from "@/components/layout"
import { DataTable } from "@/components/tables/reusable-table"
import { columns } from "@/components/tables/users-table/columns"
import { Button } from "@/components/ui/button"
import { Heading } from "@/components/ui/heading"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { User } from "@/types/user"

const breadcrumbItems = [
  { title: "Dashboard", link: "/dashboard" },
  { title: "Users", link: "/dashboard/users" },
]

const UsersPage = () => {
  return (
    <Container>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <CustomBreadcrumbs items={breadcrumbItems} />
        <div className='flex items-start justify-between'>
          <Heading
            title={`Users`}
            description='Manage Users easily from here'
          />
          <Link href={"/dashboard/users/new"}>
            <Button className='text-xs md:text-sm'>
              <Plus className='mr-2 h-4 w-4' /> Add New
            </Button>
          </Link>
        </div>
        <Separator />

        <Suspense>
          <Users />
        </Suspense>
      </div>
    </Container>
  )
}

const Users = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const pageNumber = Number(searchParams.get("page")) || 1
  const pageLimit = Number(searchParams.get("limit")) || 12
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState<string>("")

  const {
    data: userResult,
    isLoading: usersLoading,
    isError: usersError,
  } = useQuery({
    queryKey: ["all-users", pageNumber, pageLimit],
    queryFn: async () => await fetchUsersWithPagination(pageNumber, pageLimit),
  })

  // Mock delete function - replace with actual implementation
  const handleDeleteUser = (id: string) => {
    if (window.confirm("Are you sure you want to delete this user?")) {
      // Implement actual delete functionality
      console.log(`Deleting user with ID: ${id}`)
      toast.success("User deleted successfully")
      // Refresh data
      queryClient.invalidateQueries({ queryKey: ["all-users"] })
    }
  }

  const { data, totalDocuemts, currentPage, totalPages } = userResult || {}
  const users = data || []

  // Filter users based on search query
  const filteredUsers = searchQuery.trim()
    ? users.filter(
        (user: User) =>
          user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.lastName?.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : users

  if (usersError) {
    console.error("Error fetching users:", usersError)
  }

  return (
    <Container>
      <div className='mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='relative w-full sm:max-w-xs'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search users...'
            className='w-full pl-8'
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchQuery(e.target.value)
            }
          />
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='icon' className='h-9 w-9'>
            <Filter className='h-4 w-4' />
          </Button>
          <Tabs
            defaultValue={viewMode}
            className='w-[120px]'
            onValueChange={(value: string) =>
              setViewMode(value as "grid" | "list")
            }
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger
                value='grid'
                className='flex items-center justify-center'
              >
                <Grid3X3 className='h-4 w-4' />
              </TabsTrigger>
              <TabsTrigger
                value='list'
                className='flex items-center justify-center'
              >
                <List className='h-4 w-4' />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs
        defaultValue={viewMode}
        className='w-full'
        value={viewMode}
        onValueChange={(value: string) => setViewMode(value as "grid" | "list")}
      >
        <TabsContent value='grid' className='mt-0'>
          {usersLoading ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {Array.from({ length: 8 }).map((_, index) => (
                <div
                  key={index}
                  className='h-[300px] animate-pulse rounded-xl bg-muted'
                />
              ))}
            </div>
          ) : filteredUsers.length > 0 ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {filteredUsers.map((user: User) => (
                <UserCard
                  key={user._id}
                  user={user}
                  onDelete={handleDeleteUser}
                />
              ))}
            </div>
          ) : (
            <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
              <p className='text-xl font-medium'>No users found</p>
              <p className='text-muted-foreground'>
                Try adjusting your search or filters
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value='list' className='mt-0'>
          <DataTable<typeof data, User>
            pageNo={currentPage}
            columns={columns}
            data={filteredUsers}
            searchKey='email'
            isLoading={usersLoading}
            pageCount={totalPages}
            totalCount={totalDocuemts}
          />
        </TabsContent>
      </Tabs>

      {viewMode === "grid" && filteredUsers.length > 0 && (
        <div className='mt-8 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage > 1) {
                  router.push(
                    `/dashboard/users?page=${currentPage - 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage <= 1}
            >
              Previous
            </Button>
            <span className='text-sm text-muted-foreground'>
              Page {currentPage} of {totalPages || 1}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage < (totalPages || 1)) {
                  router.push(
                    `/dashboard/users?page=${currentPage + 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage >= (totalPages || 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </Container>
  )
}

export default UsersPage
