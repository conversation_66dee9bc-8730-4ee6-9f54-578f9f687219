"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { createUser, fetchUserById, updateUser } from "@/actions/users" // Assuming you have these API functions
import { UserForm } from "@/components/forms/user-form"
import { Container } from "@/components/layout"
import { UserFormData } from "@/types/user"

export default function CreateEditUserPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  // Fetch user data if editing (not creating)
  const {
    data: userData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["user", id],
    queryFn: () => fetchUserById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing user
  })

  // Mutation for creating or updating a user
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    mutationFn: id === "new" ? createUser : updateUser,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          `User ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/users") // Redirect to products list after creating or updating
      } else {
        // toast.error("Error creating or updating product");
        toast.error(`Error ${id === "new" ? "creating" : "updating"} User`)
        toast.error(data.message)
      }
    },
  })

  const handleSubmit = async (data: UserFormData) => {
    if (id === "new") {
      // Create new user
      mutate(data)
    } else {
      // Update existing user
      mutate({ ...data, _id: id! })
    }
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching user data</div> // Handle error state
  }
  if (userData && !userData.success) {
    return <div>Error fetching user data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New User" : "Edit User"}
      </h1>
      <UserForm
        initialData={userData?.user} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
      />
    </Container>
  )
}
