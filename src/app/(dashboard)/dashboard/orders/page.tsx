"use client"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Filter, Grid3X3, List, Plus, Search } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Suspense, useState } from "react"
import { toast } from "sonner"

import { fetchOrdersWithPagination } from "@/actions/orders"
import { OrderCard } from "@/components/cards"
import { Container } from "@/components/layout"
import { columns } from "@/components/tables/orders-table/columns"
import { DataTable } from "@/components/tables/reusable-table"
import { Button } from "@/components/ui/button"
import { Heading } from "@/components/ui/heading"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Order } from "@/types/order"

const OrdersPage = () => {
  return (
    <Container>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-start justify-between'>
          <Heading
            title={`Orders`}
            description='Manage Orders easily from here'
          />
          <Link href={"/dashboard/orders/new"}>
            <Button className='text-xs md:text-sm'>
              <Plus className='mr-2 h-4 w-4' /> Add New
            </Button>
          </Link>
        </div>
        <Separator />

        <Suspense>
          <Orders />
        </Suspense>
      </div>
    </Container>
  )
}

const Orders = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const pageNumber = Number(searchParams.get("page")) || 1
  const pageLimit = Number(searchParams.get("limit")) || 12
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState<string>("")

  const {
    data: orderResult,
    isLoading: ordersLoading,
    isError: ordersError,
  } = useQuery({
    queryKey: ["orders", pageNumber, pageLimit],
    queryFn: async () => await fetchOrdersWithPagination(pageNumber, pageLimit),
  })

  // Mock delete function - replace with actual implementation
  const handleDeleteOrder = (id: string) => {
    if (window.confirm("Are you sure you want to delete this order?")) {
      // Implement actual delete functionality
      console.log(`Deleting order with ID: ${id}`)
      toast.success("Order deleted successfully")
      // Refresh data
      queryClient.invalidateQueries({ queryKey: ["orders"] })
    }
  }

  const { data, totalDocuemts, currentPage, totalPages } = orderResult || {}
  const orders = data || []

  // Filter orders based on search query
  const filteredOrders = searchQuery.trim()
    ? orders.filter(
        (order: Order) =>
          order._id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.deliveryEmail
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          order.status?.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : orders

  if (ordersError) {
    console.error("Error fetching orders:", ordersError)
  }

  return (
    <Container>
      <div className='mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='relative w-full sm:max-w-xs'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search orders...'
            className='w-full pl-8'
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchQuery(e.target.value)
            }
          />
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='icon' className='h-9 w-9'>
            <Filter className='h-4 w-4' />
          </Button>
          <Tabs
            defaultValue={viewMode}
            className='w-[120px]'
            onValueChange={(value: string) =>
              setViewMode(value as "grid" | "list")
            }
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger
                value='grid'
                className='flex items-center justify-center'
              >
                <Grid3X3 className='h-4 w-4' />
              </TabsTrigger>
              <TabsTrigger
                value='list'
                className='flex items-center justify-center'
              >
                <List className='h-4 w-4' />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs
        defaultValue={viewMode}
        className='w-full'
        value={viewMode}
        onValueChange={(value: string) => setViewMode(value as "grid" | "list")}
      >
        <TabsContent value='grid' className='mt-0'>
          {ordersLoading ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {Array.from({ length: 8 }).map((_, index) => (
                <div
                  key={index}
                  className='h-[250px] animate-pulse rounded-xl bg-muted'
                />
              ))}
            </div>
          ) : filteredOrders.length > 0 ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {filteredOrders.map((order: Order) => (
                <OrderCard
                  key={order._id}
                  order={order}
                  onDelete={handleDeleteOrder}
                />
              ))}
            </div>
          ) : (
            <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
              <p className='text-xl font-medium'>No orders found</p>
              <p className='text-muted-foreground'>
                Try adjusting your search or filters
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value='list' className='mt-0'>
          <DataTable<typeof data, Order>
            pageNo={currentPage}
            columns={columns}
            data={filteredOrders}
            searchKey='deliveryEmail'
            isLoading={ordersLoading}
            pageCount={totalPages}
            totalCount={totalDocuemts}
          />
        </TabsContent>
      </Tabs>

      {viewMode === "grid" && filteredOrders.length > 0 && (
        <div className='mt-8 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage > 1) {
                  router.push(
                    `/dashboard/orders?page=${currentPage - 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage <= 1}
            >
              Previous
            </Button>
            <span className='text-sm text-muted-foreground'>
              Page {currentPage} of {totalPages || 1}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage < (totalPages || 1)) {
                  router.push(
                    `/dashboard/orders?page=${currentPage + 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage >= (totalPages || 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </Container>
  )
}

export default OrdersPage
