"use client"
import { useQuery } from "@tanstack/react-query"
import { DeleteIcon, EditIcon, Plus, Search } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import { Suspense, useCallback, useState } from "react"

import {
  deleteGalleryItems,
  fetchGalleryItemsWithPagination,
} from "@/actions/gallery"
import { Container } from "@/components/layout"
import { Button } from "@/components/ui/button"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Heading } from "@/components/ui/heading"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/functions"
import { Gallery } from "@/types/gallery"

const GalleryPage = () => {
  return (
    <Container>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-start justify-between'>
          <Heading
            title={`Gallery Images`}
            description='Manage Gallery images easily from here'
          />

          <div className='flex items-center justify-center gap-2'>
            <Link href={"/dashboard/gallery/new"}>
              <Button className='text-xs md:text-sm'>
                <Plus className='mr-2 h-4 w-4' /> Add New
              </Button>
            </Link>
          </div>
        </div>
        <Separator />

        <Suspense>
          <CustomGallery />
        </Suspense>
      </div>
    </Container>
  )
}

const CustomGallery = () => {
  const router = useRouter()
  const pathname = usePathname()

  const searchParams = useSearchParams()
  const pageNumber = Number(searchParams.get("page")) || 1
  const pageLimit = Number(searchParams.get("limit")) || 20
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState<string>("")

  const {
    data: galleryResult,
    isLoading: galleryLoading,
    isError: galleryError,
    refetch: refetchGallery,
  } = useQuery({
    queryKey: ["gallery-images", pageNumber, pageLimit],
    queryFn: async () =>
      await fetchGalleryItemsWithPagination(pageNumber, pageLimit),
  })

  const { data, totalDocuments, totalPages, currentPage } = galleryResult || {}
  const galleryItems = data || []

  // Filter gallery items based on search query
  const filteredGalleryItems = searchQuery.trim()
    ? galleryItems.filter(
        (item: Gallery) =>
          item.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description?.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : galleryItems

  const handleDelete = async (ids: string[]) => {
    if (
      window.confirm(`Are you sure you want to delete ${ids.length} items?`)
    ) {
      await deleteGalleryItems(ids)
      refetchGallery()
      setSelectedItems([])
    }
  }

  const handleSelectItem = (id: string) => {
    setSelectedItems((prev) => {
      if (prev.includes(id)) {
        return prev.filter((item) => item !== id)
      }
      return [...prev, id]
    })
  }

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString())
      params.set(name, value)

      return params.toString()
    },
    [searchParams],
  )

  if (galleryError) {
    console.error("Error fetching gallery images:", galleryError)
  }

  return (
    <Container>
      <div className='mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='relative w-full sm:max-w-xs'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search gallery...'
            className='w-full pl-8'
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchQuery(e.target.value)
            }
          />
        </div>
        {selectedItems.length > 0 && (
          <Button
            variant={"destructive"}
            onClick={() => handleDelete(selectedItems)}
          >
            <DeleteIcon className='mr-2 h-4 w-4' /> Delete{" "}
            {selectedItems.length} Items
          </Button>
        )}
      </div>

      <ScrollArea className='h-[calc(80vh-180px)] rounded-md border p-4'>
        {galleryLoading ? (
          <div className='grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5'>
            {Array.from({ length: 20 }).map((_, index) => (
              <div
                key={index}
                className='h-[200px] animate-pulse rounded-xl bg-muted'
              />
            ))}
          </div>
        ) : filteredGalleryItems.length > 0 ? (
          <div className='grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5'>
            {filteredGalleryItems.map((gallery: Gallery) => (
              <GalleryImage
                key={gallery._id}
                galleryItem={gallery}
                selected={selectedItems.includes(gallery._id ?? "")}
                handleSelectItem={handleSelectItem}
              />
            ))}
          </div>
        ) : (
          <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
            <p className='text-xl font-medium'>No images found</p>
            <p className='text-muted-foreground'>
              Try adjusting your search or add new images
            </p>
          </div>
        )}
      </ScrollArea>

      <div className='mt-8 flex items-center justify-center'>
        <div className='flex items-center space-x-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={() => {
              if (currentPage > 1) {
                router.push(
                  pathname +
                    "?" +
                    createQueryString("page", (currentPage - 1).toString()),
                )
              }
            }}
            disabled={currentPage <= 1}
          >
            Previous
          </Button>
          <span className='text-sm text-muted-foreground'>
            Page {currentPage} of {totalPages || 1}
          </span>
          <Button
            variant='outline'
            size='sm'
            onClick={() => {
              if (currentPage < (totalPages || 1)) {
                router.push(
                  pathname +
                    "?" +
                    createQueryString("page", (currentPage + 1).toString()),
                )
              }
            }}
            disabled={currentPage >= (totalPages || 1)}
          >
            Next
          </Button>
        </div>
      </div>
    </Container>
  )
}

const GalleryImage = ({
  galleryItem,
  selected,
  handleSelectItem,
}: {
  galleryItem: Gallery
  selected: boolean
  handleSelectItem: (id: string) => void
}) => {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      onClick={() => handleSelectItem(galleryItem._id ?? "")}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        "group relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md",
        selected && "border-2 border-primary bg-secondary/30",
      )}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative overflow-hidden rounded-lg'>
        <Image
          src={galleryItem.imageUrl}
          alt={galleryItem.title || "Gallery image"}
          className='h-auto w-full rounded-lg object-cover'
          width={200}
          height={200}
        />
        <div className='absolute inset-0 flex flex-col justify-between bg-gradient-to-t from-black/70 to-transparent p-3 opacity-0 transition-opacity duration-300 group-hover:opacity-100'>
          <div className='flex justify-end'>
            <Link href={`/dashboard/gallery/${galleryItem._id}`}>
              <Button size='sm' variant='secondary' className='h-8 w-8 p-0'>
                <EditIcon className='h-4 w-4' />
              </Button>
            </Link>
          </div>
          <div>
            {galleryItem.title && (
              <h3 className='line-clamp-1 text-sm font-medium text-white'>
                {galleryItem.title}
              </h3>
            )}
            {galleryItem.description && (
              <p className='line-clamp-2 text-xs text-white/80'>
                {galleryItem.description}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default GalleryPage
