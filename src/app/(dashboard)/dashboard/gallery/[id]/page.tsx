"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import {
  createGalleryItem,
  fetchGalleryItemById,
  updateGalleryItem,
} from "@/actions/gallery" // Assuming you have these API functions
import { GalleryForm } from "@/components/gallery/image-upload-form"
import { Container } from "@/components/layout"
import { GalleryFormData } from "@/types/gallery"

export default function CreateEditGalleryPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  // Fetch gallery data if editing (not creating)
  const {
    data: galleryData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["gallery", id],
    queryFn: () => fetchGalleryItemById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing gallery
  })

  // Mutation for creating or updating a gallery
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    mutationFn: id === "new" ? createGalleryItem : updateGalleryItem,
    onSuccess: (data) => {
      // console.log(data)
      if (data && data.success) {
        toast.success(
          `Gallery ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/gallery") // Redirect to galleries list after creating or updating
      } else {
        // toast.error("Error creating or updating gallery");
        toast.error(`Error ${id === "new" ? "creating" : "updating"} Gallery`)
      }
    },
  })

  // console.log("mutation data", data);

  const handleSubmit = async (data: GalleryFormData) => {
    if (id === "new") {
      // Create new gallery
      mutate(data)
    } else {
      // Update existing gallery
      mutate({ ...data, _id: id! })
    }
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching gallery data</div> // Handle error state
  }
  if (galleryData && !galleryData.success) {
    return <div>Error fetching gallery data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New Gallery" : "Edit Gallery"}
      </h1>
      <GalleryForm
        initialData={galleryData?.data} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
      />
    </Container>
  )
}
