"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import {
  createCategory,
  fetchCategories,
  fetchCategoryById,
  updateCategory,
} from "@/actions/categories" // Assuming you have these API functions
import { CategoryForm } from "@/components/forms/category-form"
import { Container } from "@/components/layout"
import { CategoryFormData } from "@/types/category"

export default function CreateEditCategoryPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  const {
    data: categoriesResult,
    isLoading: categoriesLoading,
    isError: categoriesError,
  } = useQuery({
    queryKey: ["all-categories"],
    queryFn: async () => await fetchCategories(),
  })

  // Fetch category data if editing (not creating)
  const {
    data: categoryData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["category", id],
    queryFn: () => fetchCategoryById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing category
  })

  // Mutation for creating or updating a category
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    // mutationFn: createCategory,
    mutationFn: id === "new" ? createCategory : updateCategory,
    onSuccess: (data) => {
      if (data?.success) {
        toast.success(
          `Category ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/categories") // Redirect to categories list after creating
      } else {
        // toast.error(data?.message || "Error creating/updating category");
        toast.error(`Error ${id === "new" ? "creating" : "updating"} Category`)
      }
    },
    onError: (error) => {
      toast.error(`Error ${id === "new" ? "creating" : "updating"} Category`, {
        description: error.message,
      })
    },
  })

  // const { mutate, data, error, isPending, isSuccess } = useMutation({
  //   mutationFn: updateCategory,
  //   onSuccess: () => {
  //     router.push("/dashboard/categories"); // Redirect to categories list after updating
  //   },
  // });

  const handleSubmit = async (data: CategoryFormData) => {
    if (id === "new") {
      // Create new category
      mutate(data)
    } else {
      // Update existing category
      mutate({ ...data, _id: id!, parent: data.parent ?? "" })
    }
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching category data</div> // Handle error state
  }
  if (categoryData && !categoryData.success) {
    return <div>Error fetching coupon data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New Category" : "Edit Category"}
      </h1>
      <CategoryForm
        initialData={categoryData?.data} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
        categories={categoriesResult?.data || []} // Pass list of categories for parent category dropdown
      />
    </Container>
  )
}
