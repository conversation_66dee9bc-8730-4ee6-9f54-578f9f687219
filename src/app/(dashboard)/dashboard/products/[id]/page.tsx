"use client"

import { useUser } from "@clerk/nextjs"
import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { fetchCategories } from "@/actions/categories"
import {
  createProduct,
  fetchProductByIdAdmin,
  updateProduct,
} from "@/actions/products" // Assuming you have these API functions
import { ProductForm } from "@/components/forms/product-form"
import { Container } from "@/components/layout"
import { ProductFormData } from "@/types/product"

export default function CreateEditProductPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()
  const { isLoaded, isSignedIn, user } = useUser()
  console.log("user", user)
  // Fetch product data if editing (not creating)
  const {
    data: productData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["product", id],
    queryFn: async () => await fetchProductByIdAdmin(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing product
  })

  // fetch categories using useQuery
  const { data: categoriesResult, isLoading: categoriesLoading } = useQuery({
    queryKey: ["categories"],
    queryFn: () => fetchCategories(),
  })

  console.log("categories", categoriesResult)
  // Mutation for creating or updating a product
  const { mutate } = useMutation({
    mutationFn: id === "new" ? createProduct : updateProduct,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          `Product ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/products") // Redirect to products list after creating or updating
      } else {
        // toast.error("Error creating or updating product");
        toast.error(`Error ${id === "new" ? "creating" : "updating"} Product`)
        toast.error(data.message)
      }
    },
  })

  const handleSubmit = async (data: ProductFormData) => {
    // console.log(data)
    if (id === "new") {
      // Create new product
      mutate(data)
    } else {
      // Update existing product
      mutate({ ...data, _id: id! })
    }
  }

  return (
    <Container className='p-5 md:p-10'>
      <div className='mb-8 flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold'>
            {id === "new" ? "Create New Product" : "Edit Product"}
          </h1>
          <p className='mt-1 text-sm text-muted-foreground'>
            {id === "new"
              ? "Add a new product to your catalog"
              : "Update your product details"}
          </p>
        </div>
        <button
          onClick={() => router.back()}
          className='rounded-md border px-4 py-2 text-sm font-medium transition-colors hover:bg-secondary'
        >
          Back
        </button>
      </div>

      {isLoading ? (
        <div className='flex h-[400px] w-full items-center justify-center'>
          <div className='flex flex-col items-center gap-2'>
            <div className='h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent' />
            <p className='text-sm text-muted-foreground'>
              Loading product data...
            </p>
          </div>
        </div>
      ) : isError || (productData && !productData.success) ? (
        <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
          <p className='text-xl font-medium text-destructive'>
            Error fetching product data
          </p>
          <p className='text-muted-foreground'>
            Please try again or contact support
          </p>
          <button
            onClick={() => router.push("/dashboard/products")}
            className='mt-4 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground'
          >
            Return to Products
          </button>
        </div>
      ) : (
        <div className='rounded-lg border bg-card p-6 shadow-sm'>
          <ProductForm
            initialData={productData?.data} // Populate form with existing data for edit or empty for new
            onSubmit={handleSubmit}
            categories={categoriesResult?.data || []} // Pass categories data to form
          />
        </div>
      )}
    </Container>
  )
}
