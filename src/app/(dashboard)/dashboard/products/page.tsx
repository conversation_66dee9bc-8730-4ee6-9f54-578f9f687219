"use client"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { Filter, Grid3X3, List, Plus, Search } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Suspense, useState } from "react"
import { toast } from "sonner"

import { deleteProduct, fetchProductsWithPagination } from "@/actions/products"
import { ProductCard } from "@/components/cards/product-card"
import { CustomBreadcrumbs } from "@/components/custom"
import { Container } from "@/components/layout"
import { columns } from "@/components/tables/products-table/columns"
import { DataTable } from "@/components/tables/reusable-table"
import { Button } from "@/components/ui/button"
import { Heading } from "@/components/ui/heading"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Product } from "@/types/product"

const breadcrumbItems = [
  { title: "Dashboard", link: "/dashboard" },
  { title: "Products", link: "/dashboard/products" },
]

const ProductsPage = () => {
  return (
    <Container>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <CustomBreadcrumbs items={breadcrumbItems} />
        <div className='flex items-start justify-between'>
          <Heading
            title={`Products`}
            description='Manage Products easily from here'
          />
          <Link href={"/dashboard/products/new"}>
            <Button className='text-xs md:text-sm'>
              <Plus className='mr-2 h-4 w-4' /> Add New
            </Button>
          </Link>
        </div>
        <Separator />

        <Suspense>
          <Products />
        </Suspense>
      </div>
    </Container>
  )
}

const Products = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const pageNumber = Number(searchParams.get("page")) || 1
  const pageLimit = Number(searchParams.get("limit")) || 12
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState<string>("")

  const {
    data: productResult,
    isLoading: productsLoading,
    isError: productsError,
  } = useQuery({
    queryKey: ["products", pageNumber, pageLimit],
    queryFn: async () =>
      await fetchProductsWithPagination(pageNumber, pageLimit),
  })

  const { mutate: deleteProductMutation } = useMutation({
    mutationFn: deleteProduct,
    onSuccess: () => {
      toast.success("Product deleted successfully")
      queryClient.invalidateQueries({ queryKey: ["products"] })
    },
    onError: (error) => {
      toast.error("Failed to delete product")
      console.error("Error deleting product:", error)
    },
  })

  const handleDeleteProduct = (id: string) => {
    if (window.confirm("Are you sure you want to delete this product?")) {
      deleteProductMutation(id)
    }
  }

  const { data, totalDocuemts, currentPage, totalPages } = productResult || {}
  const products = data || []

  // Filter products based on search query
  const filteredProducts = searchQuery.trim()
    ? products.filter(
        (product: Product) =>
          product.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.description
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()),
      )
    : products

  if (productsError) {
    console.error("Error fetching products:", productsError)
  }

  return (
    <Container>
      <div className='mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='relative w-full sm:max-w-xs'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search products...'
            className='w-full pl-8'
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='icon' className='h-9 w-9'>
            <Filter className='h-4 w-4' />
          </Button>
          <Tabs
            defaultValue={viewMode}
            className='w-[120px]'
            onValueChange={(value) => setViewMode(value as "grid" | "list")}
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger
                value='grid'
                className='flex items-center justify-center'
              >
                <Grid3X3 className='h-4 w-4' />
              </TabsTrigger>
              <TabsTrigger
                value='list'
                className='flex items-center justify-center'
              >
                <List className='h-4 w-4' />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs
        defaultValue={viewMode}
        className='w-full'
        value={viewMode}
        onValueChange={(value) => setViewMode(value as "grid" | "list")}
      >
        <TabsContent value='grid' className='mt-0'>
          {productsLoading ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
              {Array.from({ length: 8 }).map((_, index) => (
                <div
                  key={index}
                  className='h-[300px] animate-pulse rounded-xl bg-muted'
                />
              ))}
            </div>
          ) : filteredProducts.length > 0 ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3'>
              {filteredProducts.map((product: Product) => (
                <ProductCard
                  key={product._id}
                  product={product}
                  onDelete={handleDeleteProduct}
                />
              ))}
            </div>
          ) : (
            <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
              <p className='text-xl font-medium'>No products found</p>
              <p className='text-muted-foreground'>
                Try adjusting your search or filters
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value='list' className='mt-0'>
          <DataTable<typeof data, Product>
            pageNo={currentPage}
            columns={columns}
            data={filteredProducts}
            searchKey='name'
            isLoading={productsLoading}
            pageCount={totalPages}
            totalCount={totalDocuemts}
          />
        </TabsContent>
      </Tabs>

      {viewMode === "grid" && filteredProducts.length > 0 && (
        <div className='mt-8 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage > 1) {
                  router.push(
                    `/dashboard/products?page=${currentPage - 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage <= 1}
            >
              Previous
            </Button>
            <span className='text-sm text-muted-foreground'>
              Page {currentPage} of {totalPages || 1}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage < (totalPages || 1)) {
                  router.push(
                    `/dashboard/products?page=${currentPage + 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage >= (totalPages || 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </Container>
  )
}

export default ProductsPage
