"use client"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Filter, Grid3X3, List, Plus, Search } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Suspense, useState } from "react"
import { toast } from "sonner"

import { fetchPaymentsWithPagination } from "@/actions/payments"
import { PaymentCard } from "@/components/cards"
import { Container } from "@/components/layout"
import { columns } from "@/components/tables/payments-table/columns"
import { DataTable } from "@/components/tables/reusable-table"
import { Button } from "@/components/ui/button"
import { Heading } from "@/components/ui/heading"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Payment } from "@/types/payment"

const PaymentsPage = () => {
  return (
    <Container>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-start justify-between'>
          <Heading
            title={`Payments`}
            description='Manage Payments easily from here'
          />
          <Link href={"/dashboard/payments/new"}>
            <Button className='text-xs md:text-sm'>
              <Plus className='mr-2 h-4 w-4' /> Add New
            </Button>
          </Link>
        </div>
        <Separator />

        <Suspense>
          <Payments />
        </Suspense>
      </div>
    </Container>
  )
}

const Payments = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const pageNumber = Number(searchParams.get("page")) || 1
  const pageLimit = Number(searchParams.get("limit")) || 12
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState<string>("")

  const {
    data: paymentResult,
    isLoading: paymentsLoading,
    isError: paymentsError,
  } = useQuery({
    queryKey: ["payments", pageNumber, pageLimit],
    queryFn: async () =>
      await fetchPaymentsWithPagination(pageNumber, pageLimit),
  })

  // Mock delete function - replace with actual implementation
  const handleDeletePayment = (id: string) => {
    if (window.confirm("Are you sure you want to delete this payment?")) {
      // Implement actual delete functionality
      console.log(`Deleting payment with ID: ${id}`)
      toast.success("Payment deleted successfully")
      // Refresh data
      queryClient.invalidateQueries({ queryKey: ["payments"] })
    }
  }

  const { data, totalDocuemts, currentPage, totalPages } = paymentResult || {}
  const payments = data || []

  // Filter payments based on search query
  const filteredPayments = searchQuery.trim()
    ? payments.filter(
        (payment: Payment) =>
          payment._id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          payment.user?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          payment.status?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          payment.method?.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : payments

  if (paymentsError) {
    console.error("Error fetching payments:", paymentsError)
  }

  return (
    <Container>
      <div className='mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='relative w-full sm:max-w-xs'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search payments...'
            className='w-full pl-8'
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchQuery(e.target.value)
            }
          />
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='icon' className='h-9 w-9'>
            <Filter className='h-4 w-4' />
          </Button>
          <Tabs
            defaultValue={viewMode}
            className='w-[120px]'
            onValueChange={(value: string) =>
              setViewMode(value as "grid" | "list")
            }
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger
                value='grid'
                className='flex items-center justify-center'
              >
                <Grid3X3 className='h-4 w-4' />
              </TabsTrigger>
              <TabsTrigger
                value='list'
                className='flex items-center justify-center'
              >
                <List className='h-4 w-4' />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs
        defaultValue={viewMode}
        className='w-full'
        value={viewMode}
        onValueChange={(value: string) => setViewMode(value as "grid" | "list")}
      >
        <TabsContent value='grid' className='mt-0'>
          {paymentsLoading ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {Array.from({ length: 8 }).map((_, index) => (
                <div
                  key={index}
                  className='h-[250px] animate-pulse rounded-xl bg-muted'
                />
              ))}
            </div>
          ) : filteredPayments.length > 0 ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {filteredPayments.map((payment: Payment) => (
                <PaymentCard
                  key={payment._id}
                  payment={payment}
                  onDelete={handleDeletePayment}
                />
              ))}
            </div>
          ) : (
            <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
              <p className='text-xl font-medium'>No payments found</p>
              <p className='text-muted-foreground'>
                Try adjusting your search or filters
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value='list' className='mt-0'>
          <DataTable<typeof data, Payment>
            pageNo={currentPage}
            columns={columns}
            data={filteredPayments}
            searchKey='user'
            isLoading={paymentsLoading}
            pageCount={totalPages}
            totalCount={totalDocuemts}
          />
        </TabsContent>
      </Tabs>

      {viewMode === "grid" && filteredPayments.length > 0 && (
        <div className='mt-8 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage > 1) {
                  router.push(
                    `/dashboard/payments?page=${currentPage - 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage <= 1}
            >
              Previous
            </Button>
            <span className='text-sm text-muted-foreground'>
              Page {currentPage} of {totalPages || 1}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage < (totalPages || 1)) {
                  router.push(
                    `/dashboard/payments?page=${currentPage + 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage >= (totalPages || 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </Container>
  )
}

export default PaymentsPage
