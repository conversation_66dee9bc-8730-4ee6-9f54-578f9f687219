"use client"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { Filter, Grid3X3, List, Plus, Search } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Suspense, useState } from "react"
import { toast } from "sonner"

import { deleteBlog, fetchBlogsWithPagination } from "@/actions/blogs"
import { DashboardBlogCard } from "@/components/cards"
import { Container } from "@/components/layout"
import { columns } from "@/components/tables/blogs-table/columns"
import { DataTable } from "@/components/tables/reusable-table"
import { Button } from "@/components/ui/button"
import { Heading } from "@/components/ui/heading"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Blog } from "@/types/blog"

const breadcrumbItems = [
  { title: "Dashboard", link: "/dashboard" },
  { title: "Blogs", link: "/dashboard/blogs" },
]

const BlogsPage = () => {
  return (
    <Container>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        {/* <CustomBreadcrumbs items={breadcrumbItems} /> */}
        <div className='flex items-start justify-between'>
          <Heading
            title={`Blogs`}
            description='Manage Blogs easily from here'
          />
          <Link href={"/dashboard/blogs/new"}>
            <Button className='text-xs md:text-sm'>
              <Plus className='mr-2 h-4 w-4' /> Add New
            </Button>
          </Link>
        </div>
        <Separator />

        <Suspense>
          <Blogs />
        </Suspense>
      </div>
    </Container>
  )
}

const Blogs = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const pageNumber = Number(searchParams.get("page")) || 1
  const pageLimit = Number(searchParams.get("limit")) || 12
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState<string>("")

  const {
    data: blogResult,
    isLoading: blogsLoading,
    isError: blogsError,
  } = useQuery({
    queryKey: ["blogs", pageNumber, pageLimit],
    queryFn: async () => await fetchBlogsWithPagination(pageNumber, pageLimit),
  })

  const { mutate: deleteBlogMutation } = useMutation({
    mutationFn: deleteBlog,
    onSuccess: () => {
      toast.success("Blog deleted successfully")
      queryClient.invalidateQueries({ queryKey: ["blogs"] })
    },
    onError: (error) => {
      toast.error("Failed to delete blog")
      console.error("Error deleting blog:", error)
    },
  })

  const handleDeleteBlog = (id: string) => {
    if (window.confirm("Are you sure you want to delete this blog?")) {
      deleteBlogMutation(id)
    }
  }

  const { data, totalDocuments, currentpage, totalPages } = blogResult || {}
  const blogs = data || []

  // Filter blogs based on search query
  const filteredBlogs = searchQuery.trim()
    ? blogs.filter(
        (blog: Blog) =>
          blog.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          blog.content?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          blog.author?.toLowerCase().includes(searchQuery.toLowerCase()),
      )
    : blogs

  if (blogsError) {
    console.error("Error fetching blogs:", blogsError)
  }

  return (
    <Container>
      <div className='mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='relative w-full sm:max-w-xs'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search blogs...'
            className='w-full pl-8'
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='icon' className='h-9 w-9'>
            <Filter className='h-4 w-4' />
          </Button>
          <Tabs
            defaultValue={viewMode}
            className='w-[120px]'
            onValueChange={(value) => setViewMode(value as "grid" | "list")}
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger
                value='grid'
                className='flex items-center justify-center'
              >
                <Grid3X3 className='h-4 w-4' />
              </TabsTrigger>
              <TabsTrigger
                value='list'
                className='flex items-center justify-center'
              >
                <List className='h-4 w-4' />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs
        defaultValue={viewMode}
        className='w-full'
        value={viewMode}
        onValueChange={(value) => setViewMode(value as "grid" | "list")}
      >
        <TabsContent value='grid' className='mt-0'>
          {blogsLoading ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3'>
              {Array.from({ length: 6 }).map((_, index) => (
                <div
                  key={index}
                  className='h-[300px] animate-pulse rounded-xl bg-muted'
                />
              ))}
            </div>
          ) : filteredBlogs.length > 0 ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3'>
              {filteredBlogs.map((blog: Blog) => (
                <DashboardBlogCard
                  key={blog._id}
                  blog={blog}
                  onDelete={handleDeleteBlog}
                />
              ))}
            </div>
          ) : (
            <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
              <p className='text-xl font-medium'>No blogs found</p>
              <p className='text-muted-foreground'>
                Try adjusting your search or filters
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value='list' className='mt-0'>
          <DataTable<typeof data, Blog>
            pageNo={currentpage}
            columns={columns}
            data={filteredBlogs}
            searchKey='title'
            isLoading={blogsLoading}
            pageCount={totalPages}
            totalCount={totalDocuments}
          />
        </TabsContent>
      </Tabs>

      {viewMode === "grid" && filteredBlogs.length > 0 && (
        <div className='mt-8 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentpage > 1) {
                  router.push(
                    `/dashboard/blogs?page=${currentpage - 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentpage <= 1}
            >
              Previous
            </Button>
            <span className='text-sm text-muted-foreground'>
              Page {currentpage} of {totalPages || 1}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentpage < (totalPages || 1)) {
                  router.push(
                    `/dashboard/blogs?page=${currentpage + 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentpage >= (totalPages || 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </Container>
  )
}

export default BlogsPage
