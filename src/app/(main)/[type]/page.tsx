import { fetchProductsByType } from "@/actions/products"
import MasonaryLayout from "@/components/custom/masonary-layout"
import { Spotlight } from "@/components/ui/spotlight"
import { SAMPLE_TEMPLATES } from "@/constants"

export default async function Page({
  params: { type },
}: {
  params: { type: string }
}) {
  const productResult = await fetchProductsByType(type)
  console.log("data", type, productResult)

  return (
    // <Background>
    <div className='relative p-5 md:pb-20'>
      <MasonaryLayout data={productResult?.data} />

      <div className='gradient fixed inset-0 left-1/2 top-0 -z-10 h-1/4 w-3/4 -translate-x-1/2 -translate-y-1/2 blur-[20rem]' />
    </div>
    // </Background>
  )
}
