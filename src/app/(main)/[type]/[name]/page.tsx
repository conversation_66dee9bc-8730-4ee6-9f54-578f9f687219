import { fetchProductBySlug } from "@/actions/products"
import { ProductNotFound } from "@/components/common/product-not-found"
import { Container, Wrapper } from "@/components/layout"
import {
  MobileNavigation,
  ProductFAQ,
  ProductFeatures,
  ProductHeader,
  ProductHighlights,
  ProductOverview,
  ProductPreview,
  ProductPricing,
  ProductReviews,
  StickyNavigation,
} from "@/components/robbin-hood"
import { Spotlight } from "@/components/ui/spotlight"

export default async function ProductPage({
  params: { type, name },
}: {
  params: { type: string; name: string }
}) {
  const productResult = await fetchProductBySlug(name)

  if (!productResult) {
    return <ProductNotFound />
  }

  const product = productResult?.data

  return (
    <Wrapper className='relative min-h-screen bg-black text-white'>
      {/* Sticky Navigation */}
      <StickyNavigation />

      <Container className='py-8 lg:py-20'>
        <Spotlight
          className='-top-40 left-0 md:-top-20 md:left-60'
          fill='rgba(255, 255, 255, 0.5)'
        />

        <div className='grid gap-8 xl:grid-cols-12'>
          {/* Main Content */}
          <div className='xl:col-span-12'>
            <div className='space-y-16'>
              {/* Product Header */}
              <section id='overview'>
                <ProductHeader product={product} />
                <div className='mt-8'>
                  <ProductOverview product={product} />
                </div>
              </section>

              {/* Product Preview */}
              <section id='preview'>
                <ProductPreview images={product.images} />
              </section>

              {/* Features and Highlights Grid */}
              <section id='features'>
                <div className='grid gap-8 lg:gap-12 xl:grid-cols-2'>
                  <ProductHighlights highlights={product.highlights} />
                  <ProductFeatures features={product.keyFeatures} />
                </div>
              </section>

              {/* Reviews and Testimonials */}
              <section id='reviews'>
                <ProductReviews />
              </section>

              {/* FAQ Section */}
              <section id='faq'>
                <ProductFAQ />
              </section>

              {/* Pricing */}
              <section id='pricing'>
                <ProductPricing price={product.price} />
              </section>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        <MobileNavigation />
      </Container>
    </Wrapper>
  )
}
