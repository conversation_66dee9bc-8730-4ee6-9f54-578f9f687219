import React from "react"

import TemplateNavbar from "@/components/dashboard/template-navbar"
import Sidebar from "@/components/main/sidebar"

interface Props {
  children: React.ReactNode
}

const TemplateLayout = ({ children }: Props) => {
  return (
    <div className='flex min-h-screen w-full flex-col'>
      {/* background efffect */}
      {/* <div
        id="home"
        className="absolute inset-0 bg-[linear-gradient(to_right,rgba(23,23,23,0.4)_1px,transparent_1px),linear-gradient(to_bottom,rgba(23,23,23,0.4)_1px,transparent_1px)] bg-[size:3rem_3rem] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_110%)] h-full mt-[63px"
      /> */}
      <TemplateNavbar />
      <div className='flex size-full flex-1 flex-col lg:flex-row'>
        <Sidebar />
        <main className='w-full pt-14 lg:ml-72 lg:max-w-[calc(100%-18rem)]'>
          {children}
        </main>
      </div>
    </div>
  )
}

export default TemplateLayout
