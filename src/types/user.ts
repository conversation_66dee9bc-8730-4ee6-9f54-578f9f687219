import { z } from "zod"

import { ROLES } from "./constants"
import { FormConfig } from "./form"

// TypeScript Interface for User
export interface User {
  _id?: string // User ID (MongoDB ObjectId as a string)
  clerkId?: string // Clerk authentication ID
  email: string // Email of the user
  password?: string // User's password (hashed, optional for some providers)
  googleId?: string // Google authentication ID (optional)
  provider: "clerk" | "google" | "email" // Provider for authentication
  firstName?: string // User's first name
  lastName?: string // User's last name
  profileImage?: string // URL for user's profile image
  phoneNumber?: string // User's phone number
  address?: Address // User's address
  role: "user" | "admin" // User role (default is "user")
  isDeleted: boolean // Whether the user is deleted
  lastLogin?: string // Timestamp of the user's last login
  createdBy: "self" | "admin" // Whether the user was created by self or admin
  createdAt?: Date // Timestamp of user creation
  updatedAt?: Date // Timestamp of last user update
}

// TypeScript Interface for Address
export interface Address {
  street?: string
  city?: string
  state?: string
  postalCode?: string
  country?: string
}

// Zod Schema for User Validation
export const userSchema = z.object({
  clerkId: z.string().optional(),
  email: z.string().min(1, "Email is required").email("Invalid email format"),
  password: z
    .string()
    .min(6, "Password must be at least 6 characters long")
    .optional(),
  googleId: z.string().optional(),
  provider: z.enum(["clerk", "google", "email"]).default("clerk"),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  profileImage: z.string().optional(),
  phoneNumber: z.string().optional(),
  address: z
    .object({
      street: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postalCode: z.string().optional(),
      country: z.string().optional(),
    })
    .optional(),
  role: z
    .enum(
      Object.values(ROLES) as [
        "user",
        "admin",
        "super_admin",
        "moderator",
        "sales",
        "support",
        "marketing",
        "accountant",
        "hr",
        "developer",
        "designer",
      ],
    )
    .default(ROLES.USER),
  isDeleted: z.boolean().default(false),
  lastLogin: z.string().optional(),
  createdBy: z.enum(["self", "admin"]).default("self"),
  createdAt: z.date(z.string()).optional(),
  // createdAt: z.string().optional(),
  // updatedAt: z.string().optional(),
})

// TypeScript Type for User Form Data
export type UserFormData = z.infer<typeof userSchema>

// Form Props for User
export interface UserFormProps {
  initialData?: UserFormData
  onSubmit: (data: UserFormData) => void
}

// User Form Config
export type UserFormConfig = FormConfig<typeof userSchema>
