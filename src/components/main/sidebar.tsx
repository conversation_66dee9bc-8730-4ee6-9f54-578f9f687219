"use client"

import { useClerk } from "@clerk/nextjs"
import { LogOutIcon } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import { SIDEBAR_LINKS } from "@/constants/links"
import { cn } from "@/functions"

import { Container } from "../layout"
import { Button, buttonVariants } from "../ui/button"

const Sidebar = () => {
  const { signOut } = useClerk()

  const pathname = usePathname()

  const handleLogout = async () => {
    await signOut()
  }

  return (
    <aside
      id='sidebar'
      className='fixed bottom-0 left-0 top-16 z-10 hidden w-72 flex-col border-r border-border/50 bg-background lg:flex'
    >
      <div className={cn("flex size-full flex-col p-3")}>
        {/* <Container delay={0.2} className="h-max">
          <Button variant="outline" className="w-full justify-between px-2">
            <span className="flex items-center gap-x-1 text-foreground/80">
              <SearchIcon className="size-4" />
              <span className="text-sm">Search...</span>
            </span>
            <span className="px-1 py-px text-xs rounded-sm bg-muted text-muted-foreground">
              ⌘K
            </span>
          </Button>
        </Container> */}
        <ul className='w-full space-y-2 py-5'>
          {SIDEBAR_LINKS.map((link, index) => {
            const isActive = pathname === link.href

            return (
              <li key={index} className='w-full'>
                <Container delay={0.1 + index / 10}>
                  <Link
                    href={link.href}
                    className={buttonVariants({
                      variant: "ghost",
                      className: isActive
                        ? "w-full !justify-start bg-muted text-primary-foreground"
                        : "w-full !justify-start text-foreground/70",
                    })}
                  >
                    <link.icon strokeWidth={2} className='mr-1.5 size-[18px]' />
                    {link.label}
                  </Link>
                </Container>
              </li>
            )
          })}
        </ul>
        <div className='mt-auto flex w-full flex-col gap-3'>
          <Container delay={0.3}>
            <div className='h-10 w-full'>
              <Button
                variant='ghost'
                onClick={handleLogout}
                className='w-full justify-start'
              >
                <LogOutIcon className='mr-1.5 size-4' />
                Logout
              </Button>
            </div>
          </Container>
        </div>
      </div>
    </aside>
  )
}

export default Sidebar
