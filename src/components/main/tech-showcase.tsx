"use client"

import { motion } from "framer-motion"
import { useState } from "react"

import { technologiesData } from "@/constants"
import type { TechnologyCategory } from "@/constants/technologies"
import { cn } from "@/lib/utils"

import { TechCard } from "../cards/tech-card"
import { SectionTitle } from "../common"
import { SectionWrapper } from "../layout"
import { Badge } from "../ui/badge"
import { Button } from "../ui/button"
import { Card, CardContent } from "../ui/card"
import { Separator } from "../ui/separator"

const TechShowcase = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)

  // Filter categories based on selected category
  const displayCategories = selectedCategory
    ? technologiesData.filter((category) => category.title === selectedCategory)
    : technologiesData

  const totalTechnologies = technologiesData.reduce(
    (total, category) => total + category.technologies.length,
    0,
  )

  return (
    <SectionWrapper>
      <SectionTitle
        title='Our Technology Stack'
        description='Explore our comprehensive collection of cutting-edge technologies across the entire development stack'
      />

      {/* Category Filter Chips */}
      <div className='mt-12 flex flex-wrap justify-center gap-3'>
        <Button
          onClick={() => setSelectedCategory(null)}
          variant={selectedCategory === null ? "default" : "outline"}
          className={cn(
            "transition-all duration-200 hover:scale-105",
            selectedCategory === null ? "shadow-lg shadow-primary/25" : "",
          )}
        >
          All Technologies
        </Button>
        {technologiesData.map((category) => (
          <Button
            key={category.title}
            onClick={() => setSelectedCategory(category.title)}
            variant={
              selectedCategory === category.title ? "default" : "outline"
            }
            className={cn(
              "transition-all duration-200 hover:scale-105",
              selectedCategory === category.title
                ? "shadow-lg shadow-primary/25"
                : "",
            )}
          >
            <span className='mr-2'>{category.icon}</span>
            {category.title}
          </Button>
        ))}
      </div>

      <Separator className='mt-12' />

      {/* Stats Section */}
      <div className='mt-16 grid grid-cols-2 gap-6 sm:grid-cols-4'>
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <Card className='p-6 text-center transition-shadow duration-300 hover:shadow-lg'>
            <CardContent className='p-0'>
              <div className='bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-4xl font-bold text-transparent'>
                {totalTechnologies}+
              </div>
              <div className='mt-2 text-sm font-medium text-muted-foreground'>
                Technologies
              </div>
            </CardContent>
          </Card>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <Card className='p-6 text-center transition-shadow duration-300 hover:shadow-lg'>
            <CardContent className='p-0'>
              <div className='bg-gradient-to-r from-primary to-accent-foreground bg-clip-text text-4xl font-bold text-transparent'>
                {technologiesData.length}
              </div>
              <div className='mt-2 text-sm font-medium text-muted-foreground'>
                Categories
              </div>
            </CardContent>
          </Card>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <Card className='p-6 text-center transition-shadow duration-300 hover:shadow-lg'>
            <CardContent className='p-0'>
              <div className='bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-4xl font-bold text-transparent'>
                100%
              </div>
              <div className='mt-2 text-sm font-medium text-muted-foreground'>
                Modern
              </div>
            </CardContent>
          </Card>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <Card className='p-6 text-center transition-shadow duration-300 hover:shadow-lg'>
            <CardContent className='p-0'>
              <div className='bg-gradient-to-r from-primary to-destructive bg-clip-text text-4xl font-bold text-transparent'>
                2025
              </div>
              <div className='mt-2 text-sm font-medium text-muted-foreground'>
                Ready
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      <Separator className='mt-12' />

      {/* Technologies Display */}
      <div className='mt-16 space-y-12'>
        {displayCategories.map(
          (category: TechnologyCategory, index: number) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className='space-y-8'
            >
              {/* Category Header */}
              <div className='flex items-center gap-4'>
                <div className='flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-primary/10 to-accent/10 text-2xl ring-1 ring-primary/20'>
                  {category.icon}
                </div>
                <div className='flex-1'>
                  <h3 className='text-2xl font-bold tracking-tight text-foreground'>
                    {category.title}
                  </h3>
                  <p className='mt-1 text-sm text-muted-foreground'>
                    {category.description}
                  </p>
                </div>
                <Badge variant='secondary' className='hidden sm:flex'>
                  {category.technologies.length}
                </Badge>
              </div>

              {/* Technology Grid */}
              <div className='grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6'>
                {category.technologies.map((tech, techIndex) => (
                  <motion.div
                    key={tech.name}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 + techIndex * 0.05 }}
                  >
                    <TechCard
                      name={tech.name}
                      logo={tech.logo}
                      description={tech.description}
                      className='h-full'
                    />
                  </motion.div>
                ))}
              </div>

              {/* Add separator between categories except for the last one */}
              {index < displayCategories.length - 1 && (
                <Separator className='mt-8' />
              )}
            </motion.div>
          ),
        )}
      </div>
    </SectionWrapper>
  )
}

export default TechShowcase
