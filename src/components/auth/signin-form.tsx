"use client"

import { useSignIn } from "@clerk/nextjs"
import { OAuthStrategy } from "@clerk/types"
import { motion } from "framer-motion"
import { ArrowLeftIcon, MailIcon } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import React, { useEffect, useState } from "react"
import { toast } from "sonner"

import { FADE_IN_VARIANTS } from "@/constants"

import Icons from "../global/icons"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import LoadingIcon from "../ui/loading-icon"

const SignInForm = () => {
  const router = useRouter()

  const params = useSearchParams()

  const from = params.get("from")

  const { isLoaded, signIn, setActive } = useSignIn()

  const [email, setEmail] = useState<string>("")
  const [code, setCode] = useState<string>("")
  const [isEmailOpen, setIsEmailOpen] = useState<boolean>(true)
  const [isCodeSent, setIsCodeSent] = useState<boolean>(false)
  const [isEmailLoading, setIsEmailLoading] = useState<boolean>(false)
  const [isCodeLoading, setIsCodeLoading] = useState<boolean>(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState<boolean>(false)
  const [isAppleLoading, setIsAppleLoading] = useState<boolean>(false)

  const handleOAuth = async (strategy: OAuthStrategy) => {
    if (strategy === "oauth_google") {
      setIsGoogleLoading(true)
    } else {
      setIsAppleLoading(true)
    }

    try {
      await signIn?.authenticateWithRedirect({
        strategy,
        redirectUrl: "/auth/signup/sso-callback",
        redirectUrlComplete: "/auth/callback",
      })

      toast.loading(
        `Redirecting to ${strategy === "oauth_google" ? "Google" : "Apple"}...`,
      )
    } catch (error) {
      console.error(error)
      toast.error("An error occurred. Please try again.")
    }
  }

  const handleEmail = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!isLoaded) return

    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    setIsEmailLoading(true)

    try {
      await signIn.create({
        identifier: email,
      })

      await signIn.prepareFirstFactor({
        strategy: "email_code",
        emailAddressId: signIn.supportedFirstFactors!.find(
          (factor) => factor.strategy === "email_code",
        )!.emailAddressId,
      })

      setIsCodeSent(true)

      // if (signInAttempt.status === "complete") {
      //     await setActive({ session: signInAttempt.createdSessionId });
      //     setIsCodeSent(true);
      // } else {
      //     console.error(JSON.stringify(signInAttempt, null, 2));
      //     toast.error("Invalid email. Please try again.");
      // }
    } catch (error: any) {
      console.error(JSON.stringify(error, null, 2))
      switch (error.errors[0]?.code) {
        case "form_identifier_not_found":
          toast.error("This email is not registered. Please sign up first.")
          router.push("/auth/signup?from=signin")
          break
        case "too_many_attempts":
          toast.error("Too many attempts. Please try again later.")
          break
        default:
          toast.error("An error occurred. Please try again")
          break
      }
    } finally {
      setIsEmailLoading(false)
    }

    // Check if the email is in db or not or if email is already have an account
    // If email is already have an account, then show login form
    // If email is not in db, then send a code to email address
  }

  const handleVerifyCode = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!isLoaded) return

    if (!code) {
      toast.error("Please enter the code")
      return
    }

    setIsCodeLoading(true)

    try {
      const signInAttempt = await signIn.attemptFirstFactor({
        strategy: "email_code",
        code,
      })

      if (signInAttempt.status === "complete") {
        await setActive({ session: signInAttempt.createdSessionId })
        router.push("/auth/callback")
      } else {
        console.error(JSON.stringify(signInAttempt, null, 2))
        toast.error("Invalid code. Please try again.")
      }
    } catch (error: any) {
      console.error(JSON.stringify(error, null, 2))
      switch (error.errors[0]?.code) {
        case "form_code_incorrect":
          toast.error("Incorrect code. Please enter valid code.")
          break
        case "verification_failed":
          toast.error("Verification failed. Please try after some time.")
          break
        case "too_many_attempts":
          toast.error("Too many attempts. Please try again later.")
          break
        default:
          toast.error("An error occurred. Please try again")
          break
      }
    } finally {
      setIsCodeLoading(false)
    }

    // Check if the code is correct or not
    // If code is correct, then show create password form
    // If code is incorrect, then show error message
  }

  useEffect(() => {
    if (from) {
      setIsEmailOpen(false)
    }
  }, [])

  return (
    <div className='flex w-full flex-col text-center'>
      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
      >
        <div className='flex justify-center'>
          <Link href='/'>
            <Icons.icon className='h-8 w-8' />
          </Link>
        </div>
        <h1 className='mt-4 text-center text-2xl'>
          {isEmailOpen
            ? "Login to DesignByte"
            : isCodeSent
              ? "Verify your email"
              : "Welcome to DesignByte"}
        </h1>
        <p className='mt-2 text-sm text-muted-foreground'>
          {isEmailOpen
            ? "Choose a method to login"
            : isCodeSent
              ? "Please check your inbox for verification code"
              : "Enter your email address to get started"}
        </p>
      </motion.div>
      {isEmailOpen ? (
        <div>
          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
            className='flex flex-col gap-4 py-8'
          >
            <div className='w-full'>
              <Button
                size='lg'
                type='button'
                disabled={isGoogleLoading || isAppleLoading || isEmailLoading}
                onClick={() => handleOAuth("oauth_google")}
                variant='tertiary'
                className='w-full'
              >
                {isGoogleLoading ? (
                  <LoadingIcon size='sm' className='absolute left-4 h-4 w-4' />
                ) : (
                  <Icons.google className='absolute left-4 h-4 w-4' />
                )}
                Continue with Google
              </Button>
            </div>
            <div className='w-full'>
              <Button
                size='lg'
                type='button'
                disabled={isGoogleLoading || isAppleLoading || isEmailLoading}
                onClick={() => handleOAuth("oauth_apple")}
                variant='tertiary'
                className='w-full'
              >
                {isAppleLoading ? (
                  <LoadingIcon size='sm' className='absolute left-4 h-4 w-4' />
                ) : (
                  <Icons.apple className='absolute left-4 h-4 w-4' />
                )}
                Continue with Apple
              </Button>
            </div>
            <div className='w-full'>
              <Button
                size='lg'
                type='button'
                variant='tertiary'
                disabled={isGoogleLoading || isAppleLoading || isEmailLoading}
                onClick={() => setIsEmailOpen(false)}
                className='w-full'
              >
                <MailIcon className='absolute left-4 h-4 w-4' />
                Continue with email
              </Button>
            </div>
          </motion.div>
        </div>
      ) : (
        <div>
          {isCodeSent ? (
            <div>
              <motion.form
                variants={FADE_IN_VARIANTS}
                animate='visible'
                initial='hidden'
                onSubmit={handleVerifyCode}
                className='flex w-full flex-col gap-4 py-8'
              >
                <div className='w-full'>
                  <Input
                    autoFocus={true}
                    name='code'
                    type='number'
                    value={code}
                    maxLength={6}
                    disabled={isCodeLoading}
                    onChange={(e) => setCode(e.target.value)}
                    placeholder='Enter the verification code'
                    className='w-full'
                  />
                </div>
                <div className='w-full'>
                  <Button
                    type='submit'
                    disabled={isCodeLoading}
                    className='w-full'
                  >
                    {isCodeLoading ? (
                      <LoadingIcon size='sm' className='mr-2' />
                    ) : (
                      "Verify code"
                    )}
                  </Button>
                </div>
                <div className='flex w-full items-center gap-2'>
                  <Button
                    asChild
                    type='button'
                    disabled={isCodeLoading}
                    variant='tertiary'
                    className='w-full'
                  >
                    <Link href='https://mail.google.com' target='_blank'>
                      <Icons.gmail className='mr-2 h-4 w-4' />
                      Gmail
                    </Link>
                  </Button>
                  <Button
                    asChild
                    type='button'
                    disabled={isCodeLoading}
                    variant='tertiary'
                    className='w-full'
                  >
                    <Link href='https://outlook.live.com' target='_blank'>
                      <Icons.outlook className='mr-2 h-4 w-4' />
                      Outlook
                    </Link>
                  </Button>
                </div>
              </motion.form>
            </div>
          ) : (
            <div>
              <motion.form
                variants={FADE_IN_VARIANTS}
                animate='visible'
                initial='hidden'
                onSubmit={handleEmail}
                className='flex w-full flex-col gap-4 py-8'
              >
                <div className='w-full'>
                  <Input
                    autoFocus={true}
                    name='email'
                    type='email'
                    value={email}
                    disabled={isEmailLoading}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder='Enter your email address'
                    className='w-full'
                  />
                </div>
                <div className='w-full'>
                  <Button
                    type='submit'
                    disabled={isEmailLoading}
                    className='w-full'
                  >
                    {isEmailLoading ? (
                      <LoadingIcon size='sm' className='mr-2' />
                    ) : (
                      "Continue"
                    )}
                  </Button>
                </div>
                <div className='w-full'>
                  <Button
                    type='button'
                    variant='ghost'
                    disabled={isEmailLoading}
                    onClick={() => setIsEmailOpen(true)}
                    className='w-full'
                  >
                    <ArrowLeftIcon className='mr-2 h-3.5 w-3.5' />
                    Back
                  </Button>
                </div>
              </motion.form>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default SignInForm
