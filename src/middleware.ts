import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server"
import { NextResponse } from "next/server"

import { fetchDashboardAccess } from "./actions/users"

const isProtectedRoute = createRouteMatcher(["/dashboard(.*)"])
const isPublicRoute = createRouteMatcher(["/", "/marketing(.*)"])
const isAuthRoute = createRouteMatcher(["/auth(.*)"])

export default clerkMiddleware(async (auth, req) => {
  const { userId } = auth()

  // Allow public routes
  if (isPublicRoute(req)) {
    return NextResponse.next()
  }

  let user = null

  if (userId && isProtectedRoute(req)) {
    console.log("userId", userId)
    user = await fetchDashboardAccess(userId)
    console.log("user", user)
    if (!user?.access) {
      // return NextResponse.next();
      return NextResponse.redirect(new URL("/", req.url))
    }
  }

  // if (user && isProtectedRoute(req) && user.role !== "admin"  ) {
  //   return NextResponse.redirect(new URL("/", req.url));
  // }

  // Redirect unauthenticated users to sign in
  if (!userId && isProtectedRoute(req)) {
    return NextResponse.redirect(new URL("/auth/signin", req.url))
  }

  // Check for authenticated users trying to access auth routes
  if (userId && isAuthRoute(req)) {
    // return NextResponse.redirect(new URL("/dashboard", req.url));
    return NextResponse.redirect(new URL("/", req.url))
  }
})

export const config = {
  // matcher: ["/((?!.*\\..*|_next).*)", "/(api|trpc)(.*)"],
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
}
