{"name": "designbyte-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "pre-commit": "lint-staged", "commit": "git-cz", "commitlint": "commitlint --edit", "check": "npm run format:check && npm run lint", "fix": "npm run format && npm run lint:fix", "clean": "rimraf .next out dist", "type-check": "tsc --noEmit"}, "dependencies": {"@clerk/nextjs": "^5.3.3", "@clerk/types": "^4.14.0", "@emailjs/browser": "^4.4.1", "@headlessui/react": "^2.1.2", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.61.5", "@tanstack/react-query-devtools": "^5.61.5", "@tanstack/react-table": "^8.20.5", "@typescript-eslint/eslint-plugin": "^8.29.1", "@uploadthing/react": "^7.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "cookies-next": "^5.0.2", "date-fns": "^4.1.0", "embla-carousel-react": "^8.2.0", "framer-motion": "^11.3.28", "hamburger-react": "^2.5.1", "input-otp": "^1.2.4", "lucide-react": "^0.428.0", "mini-svg-data-uri": "^1.4.4", "next": "14.2.6", "next-themes": "^0.4.3", "nextjs-toploader": "^3.7.15", "posthog-js": "^1.193.0", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.53.0", "react-layout-masonry": "^1.2.0", "react-parallax-tilt": "^1.7.237", "react-use": "^17.5.1", "recharts": "^2.13.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.4.0", "usehooks-ts": "^3.1.0", "zod": "^3.23.8"}, "devDependencies": {"@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.1.0", "eslint-plugin-simple-import-sort": "^12.0.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.0.0", "postcss": "^8", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.6.11", "prisma": "5.21.1", "rimraf": "^5.0.5", "tailwindcss": "^3.4.1", "typescript": "^5"}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}}